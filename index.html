<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SimplePOS Hybrid - Smart Conversational + Traditional Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Modern POS Theme System - 2024 Standards */
        :root {
            /* Light Mode - Default */
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-accent: #f1f5f9;
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
            --accent-primary: #0ea5e9;
            --accent-secondary: #0284c7;
            --accent-gradient: linear-gradient(135deg, #0ea5e9, #0284c7);
            --shadow-color: rgba(14, 165, 233, 0.1);
            --border-radius: 8px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            /* Brand Customization */
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }

        /* Dark Mode - Modern AI Interface (Keep Intact) */
        .theme-dark {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-accent: #334155;
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #cbd5e1;
            --border-color: #475569;
            --accent-primary: #06b6d4;
            --accent-secondary: #0891b2;
            --accent-gradient: linear-gradient(135deg, #06b6d4, #0891b2);
            --shadow-color: rgba(6, 182, 212, 0.2);
            --border-radius: 12px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #f87171;
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }

        /* High Contrast - Accessibility Mode */
        .theme-contrast {
            --bg-primary: #ffffff;
            --bg-secondary: #ffffff;
            --bg-accent: #f5f5f5;
            --text-primary: #000000;
            --text-secondary: #000000;
            --text-muted: #333333;
            --border-color: #000000;
            --accent-primary: #0066cc;
            --accent-secondary: #004499;
            --accent-gradient: linear-gradient(135deg, #0066cc, #004499);
            --shadow-color: rgba(0, 102, 204, 0.3);
            --border-radius: 4px;
            --success-color: #008000;
            --warning-color: #ff8c00;
            --error-color: #cc0000;
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }

        /* High Contrast Button Styling */
        .theme-contrast .theme-contrast-button:disabled {
            background-color: #cccccc !important;
            color: #000000 !important;
            border: 2px solid #000000 !important;
        }

        .theme-contrast #checkoutBtn:not(:disabled) {
            background-color: var(--accent-primary) !important;
            color: #ffffff !important;
            border: 2px solid #000000 !important;
        }

        .theme-contrast #checkoutBtn:not(:disabled):hover {
            background-color: var(--accent-secondary) !important;
            border-color: #000000 !important;
        }

        /* Business Theme CSS Variables */
        /* Café Theme */
        .theme-cafe {
            --bg-primary: #faf7f2;
            --bg-secondary: #ffffff;
            --bg-accent: #f4f1ec;
            --text-primary: #3d2914;
            --text-secondary: #6b4e24;
            --text-muted: #8b6914;
            --border-color: #e6d5b7;
            --accent-primary: #8b4513;
            --accent-secondary: #d2691e;
            --accent-gradient: linear-gradient(45deg, #8b4513, #d2691e);
            --shadow-color: rgba(139, 69, 19, 0.1);
            --border-radius: 12px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }

        /* Corporate Theme */
        .theme-corporate {
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-accent: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
            --accent-primary: #3b82f6;
            --accent-secondary: #1d4ed8;
            --accent-gradient: linear-gradient(45deg, #3b82f6, #1d4ed8);
            --shadow-color: rgba(59, 130, 246, 0.1);
            --border-radius: 6px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }

        /* Restaurant Theme */
        .theme-restaurant {
            --bg-primary: #fef2f2;
            --bg-secondary: #ffffff;
            --bg-accent: #fee2e2;
            --text-primary: #7f1d1d;
            --text-secondary: #991b1b;
            --text-muted: #b91c1c;
            --border-color: #fecaca;
            --accent-primary: #dc2626;
            --accent-secondary: #ea580c;
            --accent-gradient: linear-gradient(45deg, #dc2626, #ea580c);
            --shadow-color: rgba(220, 38, 38, 0.1);
            --border-radius: 8px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }

        /* Health/Organic Theme */
        .theme-health {
            --bg-primary: #f0fdf4;
            --bg-secondary: #ffffff;
            --bg-accent: #dcfce7;
            --text-primary: #14532d;
            --text-secondary: #166534;
            --text-muted: #15803d;
            --border-color: #bbf7d0;
            --accent-primary: #16a34a;
            --accent-secondary: #059669;
            --accent-gradient: linear-gradient(45deg, #16a34a, #059669);
            --shadow-color: rgba(22, 163, 74, 0.1);
            --border-radius: 16px;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --brand-primary: var(--accent-primary);
            --brand-secondary: var(--accent-secondary);
        }


        /* Theme Transitions */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }

        /* Mobile-First Responsive Design */

        /* Ensure minimum touch targets (44px) for mobile */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }

        /* Mobile-optimized interface button */
        .interface-button {
            min-width: 120px; /* Larger mobile width for better touch */
            min-height: 44px; /* Minimum touch target */
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), all 0.3s ease;
        }

        @media (min-width: 640px) {
            .interface-button {
                min-width: 12rem; /* 192px - desktop width */
            }
        }

        /* Mobile-specific improvements */
        @media (max-width: 639px) {
            /* Better mobile spacing */
            .mobile-spacing {
                padding: 1rem 0.75rem;
            }

            /* Larger mobile text */
            .mobile-text {
                font-size: 0.875rem;
                line-height: 1.5;
            }

            /* Mobile-friendly product grid */
            .mobile-product-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }


            /* Mobile input improvements */
            .mobile-input {
                padding: 0.875rem 1rem;
                font-size: 1rem;
            }

            /* Mobile button improvements */
            .mobile-button {
                padding: 0.875rem 1.25rem;
                font-size: 0.875rem;
                min-height: 44px;
            }
        }

        /* Touch-friendly interactions */
        @media (hover: none) and (pointer: coarse) {
            /* Remove hover effects on touch devices */
            .product-card:hover {
                transform: none;
                border-color: var(--border-color) !important;
                box-shadow: none;
            }

            /* Add touch feedback instead */
            .product-card:active {
                transform: scale(0.98);
                background-color: var(--accent-primary);
                color: white;
            }

            .quick-action:hover {
                background-color: transparent;
                border-color: var(--border-color);
                transform: none;
            }

            .quick-action:active {
                background-color: var(--bg-accent);
                transform: scale(0.98);
            }
        }

        /* Component Animations */
        .message-bubble {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .product-card:hover {
            transform: translateY(-2px);
            transition: transform 0.2s ease;
            border-color: var(--accent-primary) !important;
            box-shadow: 0 4px 12px var(--shadow-color);
        }
        .typing-indicator {
            animation: pulse 1.5s infinite;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Quick Actions Enhanced Styling */
        .quick-action {
            position: relative;
            overflow: hidden;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }
        .quick-action:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: var(--accent-primary);
        }
        .quick-action::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        .quick-action:hover::before {
            left: 100%;
        }
        .quick-action .icon-container {
            transition: all 0.2s ease;
        }
        .quick-action:hover .icon-container {
            transform: scale(1.1);
        }
        .quick-action:active {
            transform: scale(0.98);
        }

        .suggestion-item:hover {
            background: var(--accent-gradient);
            color: white;
            transform: scale(1.02);
            transition: all 0.2s ease;
        }
        .chat-container {
            height: 350px;
            max-height: 350px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        /* Mobile-responsive chat container */
        @media (max-width: 639px) {
            .chat-container {
                max-height: 350px;
            }
        }
        .mode-toggle {
            background: var(--accent-gradient);
        }
        .theme-selector {
            background: var(--accent-gradient);
        }

        /* POS UI Improvements - Uniform Quick Actions (Fixed Width, No Scroll) */
        .quick-actions-compact {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            padding: 0.25rem 0;
            width: 100%;
        }

        .quick-action-icon {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 0.25rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            transition: all 0.2s ease;
            cursor: pointer;
            height: 60px; /* Fixed height for all buttons */
            max-width: none;
        }

        .quick-action-icon:hover {
            background-color: var(--bg-accent);
            border-color: var(--accent-primary);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .quick-action-icon i {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
            color: var(--accent-primary);
        }

        .quick-action-icon span {
            font-size: 0.7rem;
            font-weight: 500;
            color: var(--text-secondary);
            text-align: center;
            line-height: 1.1;
            white-space: nowrap;
        }

        /* Dropdown positioning - appear above buttons to avoid scrolling */
        .quick-action-dropdown {
            position: absolute;
            bottom: 100%; /* Position above the button */
            left: 0;
            margin-bottom: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            box-shadow: 0 -4px 12px var(--shadow-color);
            z-index: 1000;
            min-width: 160px;
        }

        /* Responsive adjustments to maintain fixed width and uniform sizing */
        @media (max-width: 768px) {
            .quick-actions-compact {
                gap: 0.75rem;
            }

            .quick-action-icon {
                padding: 0.6rem 0.2rem;
                height: 58px; /* Slightly smaller but still uniform */
            }

            .quick-action-icon i {
                font-size: 1.1rem;
            }

            .quick-action-icon span {
                font-size: 0.65rem;
            }
        }

        @media (max-width: 480px) {
            .quick-actions-compact {
                gap: 0.5rem;
            }

            .quick-action-icon {
                padding: 0.5rem 0.15rem;
                height: 55px; /* Smaller but still uniform */
            }

            .quick-action-icon i {
                font-size: 1rem;
                margin-bottom: 0.2rem;
            }

            .quick-action-icon span {
                font-size: 0.6rem;
            }
        }

        /* Floating Action Button */
        .fab-container {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            z-index: 1000;
        }

        .fab-main {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--accent-gradient);
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .fab-main:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }

        .fab-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .fab-menu.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .fab-item {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--accent-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .fab-item:hover {
            background-color: var(--accent-primary);
            color: white;
            transform: scale(1.1);
        }

        /* Horizontal Quick Actions */
        .horizontal-actions {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
            padding: 0.5rem 0;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .horizontal-actions::-webkit-scrollbar {
            display: none;
        }

        .horizontal-action {
            flex: 0 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            min-width: 80px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .horizontal-action:hover {
            background-color: var(--bg-accent);
            border-color: var(--accent-primary);
        }

        .horizontal-action i {
            font-size: 1.25rem;
            color: var(--accent-primary);
            margin-bottom: 0.25rem;
        }

        .horizontal-action span {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-align: center;
            line-height: 1.2;
        }

        /* Compact Order Summary */
        .compact-summary {
            background-color: var(--bg-accent);
            border-radius: var(--border-radius);
            padding: 0.75rem;
            margin-bottom: 1rem;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .summary-row:last-child {
            margin-bottom: 0;
            padding-top: 0.5rem;
            border-top: 1px solid var(--border-color);
            font-weight: 600;
        }
    </style>
</head>
<body class="font-sans" style="background-color: var(--bg-primary);">
    <!-- Prototype Banner -->

    <!-- Header -->
    <header class="shadow-sm" style="background-color: var(--bg-secondary); border-bottom: 1px solid var(--border-color);">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-semibold" style="color: var(--text-primary);">SimplePOS</h1>
                    <span id="modeIndicator" class="px-3 py-1 text-sm font-medium rounded-full w-28 sm:w-32 text-center" style="background-color: var(--bg-accent); color: var(--accent-primary);">Hybrid Mode</span>
                </div>
                <div class="flex items-center space-x-3 sm:space-x-4">
                    <!-- Theme Selector -->
                    <div class="flex items-center space-x-2">
                        <span class="hidden sm:inline text-sm" style="color: var(--text-muted);">Theme:</span>
                        <div class="relative">
                            <button id="themeToggle" class="theme-selector text-white px-2 py-2 text-sm font-medium transition-all duration-300 touch-target whitespace-nowrap" style="border-radius: var(--border-radius); min-width: 44px; min-height: 44px;">
                                <i class="fas fa-sun mr-1 sm:mr-2"></i><span class="hidden sm:inline" id="currentTheme">Light Mode</span>
                            </button>
                            <div id="themeDropdown" class="absolute right-0 mt-2 w-48 shadow-lg rounded-lg border hidden z-50" style="background-color: var(--bg-secondary); border-color: var(--border-color); border-radius: var(--border-radius);">
                                <div class="py-1">
                                    <!-- System Themes Section -->
                                    <div class="px-3 py-2 text-xs font-medium uppercase tracking-wide" style="color: var(--text-muted); background-color: var(--bg-accent);">
                                        System Themes
                                    </div>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="light" style="color: var(--text-primary);">
                                        <i class="fas fa-sun w-4 text-center mr-3" style="color: #0ea5e9;"></i>Light Mode
                                    </button>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="dark" style="color: var(--text-primary);">
                                        <i class="fas fa-moon w-4 text-center mr-3" style="color: #64748b;"></i>Dark Mode
                                    </button>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="contrast" style="color: var(--text-primary);">
                                        <i class="fas fa-eye w-4 text-center mr-3" style="color: var(--text-primary);"></i>High Contrast
                                    </button>
                                    
                                    <!-- Visual Divider -->
                                    <div class="my-1 border-t" style="border-color: var(--border-color);"></div>
                                    
                                    <!-- Business Themes Section -->
                                    <div class="px-3 py-2 text-xs font-medium uppercase tracking-wide" style="color: var(--text-muted); background-color: var(--bg-accent);">
                                        Business Themes
                                    </div>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="cafe" style="color: var(--text-primary);">
                                        <i class="fas fa-coffee w-4 text-center mr-3 text-amber-600"></i>Café Mode
                                    </button>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="corporate" style="color: var(--text-primary);">
                                        <i class="fas fa-building w-4 text-center mr-3 text-blue-600"></i>Corporate Mode
                                    </button>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="restaurant" style="color: var(--text-primary);">
                                        <i class="fas fa-utensils w-4 text-center mr-3 text-red-600"></i>Restaurant Mode
                                    </button>
                                    <button class="theme-option w-full text-left px-4 py-2 text-sm hover:bg-opacity-10 flex items-center" data-theme="health" style="color: var(--text-primary);">
                                        <i class="fas fa-leaf w-4 text-center mr-3 text-green-600"></i>Health Mode
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Mode Toggle -->
                    <div class="flex items-center space-x-2">
                        <span class="hidden sm:inline text-sm" style="color: var(--text-muted);">Interface:</span>
                        <button id="modeToggle" class="mode-toggle interface-button text-white px-2 py-2 text-sm font-medium touch-target whitespace-nowrap" style="border-radius: var(--border-radius); min-width: 44px; min-height: 44px;" data-current-mode="Smart Hybrid">
                            <i class="fas fa-brain mr-1 sm:mr-2"></i><span class="hidden sm:inline">Smart Hybrid</span><span class="sm:hidden">Hybrid</span>
                        </button>
                    </div>

                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Left Column - Products & Conversation -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Product Grid (Collapsible) -->
                <div id="productGrid" class="shadow-sm border transition-all duration-500" style="background-color: var(--bg-secondary); border-color: var(--border-color); border-radius: var(--border-radius);">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold" style="color: var(--text-primary);">
                                Quick Select Products
                            </h2>
                            <button id="toggleGrid" class="text-sm font-medium transition-colors" style="color: var(--accent-primary);">
                                <i class="fas fa-eye mr-1"></i>
                                <span id="gridToggleText">Hide Grid</span>
                            </button>
                        </div>
                        
                        <div id="productContainer" class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
                            <!-- Coffee -->
                            <div class="product-card cursor-pointer border-2 transition-all duration-200 p-3 sm:p-6 touch-target" style="background-color: var(--bg-accent); border-color: transparent; border-radius: var(--border-radius); min-height: 100px;" data-product="Coffee" data-price="4.50">
                                <div class="text-center">
                                    <i class="fas fa-coffee text-xl sm:text-3xl mb-1 sm:mb-3" style="color: var(--accent-primary);"></i>
                                    <h3 class="font-medium text-xs sm:text-base" style="color: var(--text-primary);">Coffee</h3>
                                    <p class="font-semibold text-xs sm:text-base" style="color: var(--accent-secondary);">$4.50</p>
                                </div>
                            </div>

                            <!-- Latte -->
                            <div class="product-card cursor-pointer border-2 transition-all duration-200 p-3 sm:p-6 touch-target" style="background-color: var(--bg-accent); border-color: transparent; border-radius: var(--border-radius); min-height: 100px;" data-product="Latte" data-price="5.25">
                                <div class="text-center">
                                    <i class="fas fa-coffee text-xl sm:text-3xl mb-1 sm:mb-3" style="color: var(--accent-primary);"></i>
                                    <h3 class="font-medium text-xs sm:text-base" style="color: var(--text-primary);">Latte</h3>
                                    <p class="font-semibold text-xs sm:text-base" style="color: var(--accent-secondary);">$5.25</p>
                                </div>
                            </div>

                            <!-- Croissant -->
                            <div class="product-card cursor-pointer border-2 transition-all duration-200 p-3 sm:p-6 touch-target" style="background-color: var(--bg-accent); border-color: transparent; border-radius: var(--border-radius); min-height: 100px;" data-product="Croissant" data-price="3.75">
                                <div class="text-center">
                                    <i class="fas fa-bread-slice text-xl sm:text-3xl mb-1 sm:mb-3" style="color: var(--accent-primary);"></i>
                                    <h3 class="font-medium text-xs sm:text-base" style="color: var(--text-primary);">Croissant</h3>
                                    <p class="font-semibold text-xs sm:text-base" style="color: var(--accent-secondary);">$3.75</p>
                                </div>
                            </div>

                            <!-- Muffin -->
                            <div class="product-card cursor-pointer border-2 transition-all duration-200 p-3 sm:p-6 touch-target" style="background-color: var(--bg-accent); border-color: transparent; border-radius: var(--border-radius); min-height: 100px;" data-product="Muffin" data-price="4.00">
                                <div class="text-center">
                                    <i class="fas fa-cookie-bite text-xl sm:text-3xl mb-1 sm:mb-3" style="color: var(--accent-primary);"></i>
                                    <h3 class="font-medium text-xs sm:text-base" style="color: var(--text-primary);">Muffin</h3>
                                    <p class="font-semibold text-xs sm:text-base" style="color: var(--accent-secondary);">$4.00</p>
                                </div>
                            </div>

                            <!-- Sandwich -->
                            <div class="product-card cursor-pointer border-2 transition-all duration-200 p-3 sm:p-6 touch-target" style="background-color: var(--bg-accent); border-color: transparent; border-radius: var(--border-radius); min-height: 100px;" data-product="Sandwich" data-price="7.50">
                                <div class="text-center">
                                    <i class="fas fa-hamburger text-xl sm:text-3xl mb-1 sm:mb-3" style="color: var(--accent-primary);"></i>
                                    <h3 class="font-medium text-xs sm:text-base" style="color: var(--text-primary);">Sandwich</h3>
                                    <p class="font-semibold text-xs sm:text-base" style="color: var(--accent-secondary);">$7.50</p>
                                </div>
                            </div>

                            <!-- Juice -->
                            <div class="product-card cursor-pointer border-2 transition-all duration-200 p-3 sm:p-6 touch-target" style="background-color: var(--bg-accent); border-color: transparent; border-radius: var(--border-radius); min-height: 100px;" data-product="Fresh Juice" data-price="5.00">
                                <div class="text-center">
                                    <i class="fas fa-glass-whiskey text-xl sm:text-3xl mb-1 sm:mb-3" style="color: var(--accent-primary);"></i>
                                    <h3 class="font-medium text-xs sm:text-base" style="color: var(--text-primary);">Fresh Juice</h3>
                                    <p class="font-semibold text-xs sm:text-base" style="color: var(--accent-secondary);">$5.00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Conversation Area -->
                <div id="conversationArea" class="shadow-sm border" style="background-color: var(--bg-secondary); border-color: var(--border-color); border-radius: var(--border-radius);">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-lg font-semibold" style="color: var(--text-primary);">
                                <i class="fas fa-comments mr-2" style="color: var(--accent-primary);"></i>
                                Conversation
                            </h2>
                            <div class="flex items-center space-x-2">
                                <button id="voiceBtn" class="p-2 rounded-lg transition-colors" style="color: var(--accent-primary);" onmouseover="this.style.backgroundColor='var(--bg-accent)'" onmouseout="this.style.backgroundColor='transparent'">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <button id="clearChat" class="p-2 rounded-lg transition-colors" style="color: var(--text-muted);" onmouseover="this.style.backgroundColor='var(--bg-accent)'" onmouseout="this.style.backgroundColor='transparent'">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Chat Messages -->
                        <div id="chatContainer" class="chat-container border rounded-lg p-3 sm:p-4 mb-4" style="border-color: var(--border-color);">
                            <div class="message-bubble mb-4">
                                <div class="flex items-start space-x-2 sm:space-x-3">
                                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: var(--accent-primary);">
                                        <i class="fas fa-robot text-sm" style="color: var(--bg-primary);"></i>
                                    </div>
                                    <div class="rounded-2xl rounded-tl-md px-3 sm:px-4 py-2 sm:py-3 max-w-xs sm:max-w-sm" style="background-color: var(--bg-accent);">
                                        <p class="text-sm sm:text-base" style="color: var(--text-primary);">Hi! I'm your smart POS assistant. You can type naturally like "2 coffees and a muffin" or click products above. How can I help you today?</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Smart Suggestions -->
                        <div id="suggestions" class="flex flex-wrap gap-2 sm:gap-3 mb-4">
                            <button class="suggestion-item px-4 py-2 text-sm transition-all duration-200 touch-target" style="background-color: var(--bg-accent); color: var(--text-secondary); border-radius: 20px; min-height: 36px;" onmouseover="this.style.background='var(--accent-gradient)'; this.style.color='white'" onmouseout="this.style.background='var(--bg-accent)'; this.style.color='var(--text-secondary)'">
                                "2 coffees"
                            </button>
                            <button class="suggestion-item px-4 py-2 text-sm transition-all duration-200 touch-target" style="background-color: var(--bg-accent); color: var(--text-secondary); border-radius: 20px; min-height: 36px;" onmouseover="this.style.background='var(--accent-gradient)'; this.style.color='white'" onmouseout="this.style.background='var(--bg-accent)'; this.style.color='var(--text-secondary)'">
                                "latte with oat milk"
                            </button>
                            <button class="suggestion-item px-4 py-2 text-sm transition-all duration-200 touch-target" style="background-color: var(--bg-accent); color: var(--text-secondary); border-radius: 20px; min-height: 36px;" onmouseover="this.style.background='var(--accent-gradient)'; this.style.color='white'" onmouseout="this.style.background='var(--bg-accent)'; this.style.color='var(--text-secondary)'">
                                "add discount 10%"
                            </button>
                            <button class="suggestion-item px-4 py-2 text-sm transition-all duration-200 touch-target" style="background-color: var(--bg-accent); color: var(--text-secondary); border-radius: 20px; min-height: 36px;" onmouseover="this.style.background='var(--accent-gradient)'; this.style.color='white'" onmouseout="this.style.background='var(--bg-accent)'; this.style.color='var(--text-secondary)'">
                                "checkout"
                            </button>
                        </div>
                        
                        <!-- Input Area -->
                        <div class="flex space-x-3 sm:space-x-4">
                            <div class="flex-1 relative">
                                <input
                                    type="text"
                                    id="chatInput"
                                    placeholder="Type '2 coffees and muffin' or tap products..."
                                    class="w-full px-4 py-4 border rounded-xl focus:ring-2 outline-none text-base touch-target"
                                    style="border-color: var(--border-color); background-color: var(--bg-primary); color: var(--text-primary); min-height: 44px;"
                                    autocomplete="off"
                                >
                                <div id="typingIndicator" class="typing-indicator absolute right-4 top-4 hidden" style="color: var(--accent-primary);">
                                    <i class="fas fa-circle text-xs"></i>
                                    <i class="fas fa-circle text-xs mx-1"></i>
                                    <i class="fas fa-circle text-xs"></i>
                                </div>
                            </div>
                            <button id="sendBtn" class="px-5 py-4 rounded-xl transition-colors font-medium touch-target" style="background-color: var(--accent-primary); color: var(--bg-primary); min-height: 44px; min-width: 44px;" onmouseover="this.style.backgroundColor='var(--accent-secondary)'" onmouseout="this.style.backgroundColor='var(--accent-primary)'">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Order Summary -->
            <div class="space-y-6">
                
                <!-- Current Order -->
                <div class="shadow-sm border" style="background-color: var(--bg-secondary); border-color: var(--border-color); border-radius: var(--border-radius);">
                    <div class="p-6">
                        <h2 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">
                            <i class="fas fa-shopping-cart mr-2" style="color: var(--accent-primary);"></i>
                            Current Order
                        </h2>
                        
                        <!-- Customer Info -->
                        <div id="customerInfo" class="hidden border p-3 mb-4" style="background-color: var(--bg-accent); border-color: var(--border-color); border-radius: var(--border-radius);">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-user" style="color: var(--accent-primary);"></i>
                                    <span class="text-sm font-medium" style="color: var(--text-primary);">Customer:</span>
                                    <span id="customerName" class="text-sm" style="color: var(--accent-secondary);"></span>
                                </div>
                                <button id="removeCustomer" class="text-sm transition-colors" style="color: var(--text-muted);" onmouseover="this.style.color='var(--error-color)'" onmouseout="this.style.color='var(--text-muted)'">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div id="orderItems" class="space-y-3 mb-4">
                            <div class="text-center py-8" style="color: var(--text-muted);">
                                <i class="fas fa-shopping-bag text-4xl mb-2" style="color: var(--text-muted); opacity: 0.5;"></i>
                                <p>No items yet</p>
                                <p class="text-sm">Start by typing or clicking products</p>
                            </div>
                        </div>
                        
                        <!-- Order Summary - Hidden in favor of compact version -->
                        <div id="originalOrderSummary" class="border-t pt-4 hidden" style="border-color: var(--border-color);">
                            <div class="flex justify-between text-sm mb-2" style="color: var(--text-secondary);">
                                <span>Subtotal:</span>
                                <span id="subtotal" style="color: var(--text-primary);">$0.00</span>
                            </div>
                            <div id="discountLine" class="flex justify-between text-sm mb-2 hidden" style="color: var(--success-color);">
                                <span id="discountLabel">Discount:</span>
                                <span id="discountAmount">-$0.00</span>
                            </div>
                            <div class="flex justify-between text-sm mb-2" style="color: var(--text-secondary);">
                                <span>Tax (8.5%):</span>
                                <span id="tax" style="color: var(--text-primary);">$0.00</span>
                            </div>
                            <div class="flex justify-between font-semibold text-lg border-t pt-2" style="color: var(--text-primary); border-color: var(--border-color);">
                                <span>Total:</span>
                                <span id="total">$0.00</span>
                            </div>
                        </div>
                        
                        <!-- Checkout Button -->
                        <button id="checkoutBtn" disabled class="w-full mt-4 py-4 rounded-lg font-medium cursor-not-allowed transition-all duration-200 touch-target theme-contrast-button" style="background-color: var(--border-color); color: var(--text-muted); min-height: 48px;">
                            <i class="fas fa-credit-card mr-2"></i>
                            Complete Order
                        </button>
                        
                        <!-- Compact Order Summary -->
                        <div class="compact-summary">
                            <div class="summary-row">
                                <span class="text-sm" style="color: var(--text-secondary);">Subtotal:</span>
                                <span id="subtotalCompact" class="text-sm font-medium" style="color: var(--text-primary);">$0.00</span>
                            </div>
                            <div id="discountLineCompact" class="summary-row hidden" style="color: var(--success-color);">
                                <span class="text-sm" id="discountLabelCompact">Discount:</span>
                                <span class="text-sm font-medium" id="discountAmountCompact">-$0.00</span>
                            </div>
                            <div class="summary-row">
                                <span class="text-sm" style="color: var(--text-secondary);">Tax (8.5%):</span>
                                <span id="taxCompact" class="text-sm font-medium" style="color: var(--text-primary);">$0.00</span>
                            </div>
                            <div class="summary-row">
                                <span class="text-lg font-semibold" style="color: var(--text-primary);">Total:</span>
                                <span id="totalCompact" class="text-lg font-semibold" style="color: var(--text-primary);">$0.00</span>
                            </div>
                        </div>

                        <!-- Quick Actions - One Line Layout -->
                        <div class="border-t pt-3 mt-3" style="border-color: var(--border-color);">
                            <h3 class="font-medium mb-3 text-sm" style="color: var(--text-primary);">Quick Actions</h3>
                            <div class="quick-actions-compact">
                                <div class="relative">
                                    <button id="discountBtn" class="quick-action-icon touch-target">
                                        <i class="fas fa-percentage"></i>
                                        <span>Discount</span>
                                    </button>
                                    <div id="discountDropdown" class="hidden quick-action-dropdown">
                                        <button class="discount-option w-full flex items-center px-4 py-2 text-sm transition-colors hover:shadow-sm border-b" style="color: var(--text-secondary); border-color: var(--border-color);" data-discount="5" onmouseover="this.style.backgroundColor='var(--bg-accent)'" onmouseout="this.style.backgroundColor='transparent'">
                                            <div class="flex items-center justify-center w-6 h-6 rounded mr-3 flex-shrink-0" style="background-color: var(--bg-accent);">
                                                <i class="fas fa-tag text-xs" style="color: var(--accent-primary);"></i>
                                            </div>
                                            <span>5% Discount</span>
                                        </button>
                                        <button class="discount-option w-full flex items-center px-4 py-2 text-sm transition-colors hover:shadow-sm border-b" style="color: var(--text-secondary); border-color: var(--border-color);" data-discount="10" onmouseover="this.style.backgroundColor='var(--bg-accent)'" onmouseout="this.style.backgroundColor='transparent'">
                                            <div class="flex items-center justify-center w-6 h-6 rounded mr-3 flex-shrink-0" style="background-color: var(--bg-accent);">
                                                <i class="fas fa-tag text-xs" style="color: var(--accent-primary);"></i>
                                            </div>
                                            <span>10% Discount</span>
                                        </button>
                                        <button class="discount-option w-full flex items-center px-4 py-2 text-sm transition-colors hover:shadow-sm border-b" style="color: var(--text-secondary); border-color: var(--border-color);" data-discount="15" onmouseover="this.style.backgroundColor='var(--bg-accent)'" onmouseout="this.style.backgroundColor='transparent'">
                                            <div class="flex items-center justify-center w-6 h-6 rounded mr-3 flex-shrink-0" style="background-color: var(--bg-accent);">
                                                <i class="fas fa-tag text-xs" style="color: var(--accent-primary);"></i>
                                            </div>
                                            <span>15% Discount</span>
                                        </button>
                                        <button class="discount-option w-full flex items-center px-4 py-2 text-sm transition-colors hover:shadow-sm" style="color: var(--text-secondary);" data-discount="20" onmouseover="this.style.backgroundColor='var(--bg-accent)'" onmouseout="this.style.backgroundColor='transparent'">
                                            <div class="flex items-center justify-center w-6 h-6 rounded mr-3 flex-shrink-0" style="background-color: var(--bg-accent);">
                                                <i class="fas fa-tag text-xs" style="color: var(--accent-primary);"></i>
                                            </div>
                                            <span>20% Discount</span>
                                        </button>
                                    </div>
                                </div>

                                <div class="relative">
                                    <button id="customerBtn" class="quick-action-icon touch-target">
                                        <i class="fas fa-user-plus"></i>
                                        <span>Customer</span>
                                    </button>
                                    <div id="customerDropdown" class="hidden quick-action-dropdown p-3" style="min-width: 200px;">
                                        <div class="mb-3">
                                            <label class="block text-xs font-medium mb-2" style="color: var(--text-secondary);">Customer Name:</label>
                                            <input type="text" id="customerNameInput" placeholder="Enter customer name" class="w-full px-3 py-2 border text-sm focus:ring-2 focus:border-transparent" style="border-color: var(--border-color); background-color: var(--bg-primary); color: var(--text-primary); border-radius: var(--border-radius);">
                                        </div>
                                        <div class="flex space-x-2">
                                            <button id="addCustomerBtn" class="flex-1 px-3 py-2 text-white text-sm hover:opacity-90 transition-opacity" style="background: var(--accent-gradient); border-radius: var(--border-radius);">
                                                Add
                                            </button>
                                            <button id="cancelCustomerBtn" class="flex-1 px-3 py-2 text-sm border transition-colors" style="background-color: var(--bg-accent); color: var(--text-secondary); border-color: var(--border-color); border-radius: var(--border-radius);" onmouseover="this.style.backgroundColor='var(--bg-primary)'" onmouseout="this.style.backgroundColor='var(--bg-accent)'">
                                                Cancel
                                            </button>
                                        </div>
                                        <div class="mt-2 text-xs" style="color: var(--text-muted);">
                                            Quick options:
                                            <button class="customer-quick hover:underline ml-1" style="color: var(--accent-primary);" data-name="Walk-in">Walk-in</button> |
                                            <button class="customer-quick hover:underline ml-1" style="color: var(--accent-primary);" data-name="Phone Order">Phone Order</button>
                                        </div>
                                    </div>
                                </div>

                                <button id="printReceiptBtn" class="quick-action-icon touch-target">
                                    <i class="fas fa-receipt" style="color: var(--success-color);"></i>
                                    <span>Receipt</span>
                                </button>

                                <button id="voidItemBtn" class="quick-action-icon touch-target">
                                    <i class="fas fa-undo" style="color: var(--error-color);"></i>
                                    <span>Void Item</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>



            </div>
        </div>
    </div>

    <!-- Floating Action Button for Quick Access -->
    <div class="fab-container">
        <div class="fab-menu" id="fabMenu">
            <button class="fab-item" id="fabDiscount" title="Apply Discount">
                <i class="fas fa-percentage"></i>
            </button>
            <button class="fab-item" id="fabCustomer" title="Add Customer">
                <i class="fas fa-user-plus"></i>
            </button>
            <button class="fab-item" id="fabReceipt" title="Print Receipt">
                <i class="fas fa-receipt"></i>
            </button>
            <button class="fab-item" id="fabVoid" title="Void Last Item">
                <i class="fas fa-undo"></i>
            </button>
        </div>
        <button class="fab-main" id="fabMain">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <!-- Alternative: Horizontal Quick Actions Bar (can be toggled) -->
    <div id="horizontalActionsBar" class="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg p-3 hidden" style="background-color: var(--bg-secondary); border-color: var(--border-color); z-index: 999;">
        <div class="max-w-7xl mx-auto">
            <div class="horizontal-actions">
                <button class="horizontal-action" id="hDiscountBtn">
                    <i class="fas fa-percentage"></i>
                    <span>Discount</span>
                </button>
                <button class="horizontal-action" id="hCustomerBtn">
                    <i class="fas fa-user-plus"></i>
                    <span>Customer</span>
                </button>
                <button class="horizontal-action" id="hReceiptBtn">
                    <i class="fas fa-receipt"></i>
                    <span>Receipt</span>
                </button>
                <button class="horizontal-action" id="hVoidBtn">
                    <i class="fas fa-undo"></i>
                    <span>Void</span>
                </button>
                <button class="horizontal-action" id="hCheckoutBtn">
                    <i class="fas fa-credit-card"></i>
                    <span>Checkout</span>
                </button>
                <button class="horizontal-action" id="hClearBtn">
                    <i class="fas fa-trash"></i>
                    <span>Clear</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Application State
        let currentOrder = [];
        let currentDiscount = { percentage: 0, amount: 0 };
        let currentCustomer = null;
        let currentTheme = 'light';
        let isProcessingCheckout = false; // Add checkout state flag
        let userPreferences = {
            preferredMode: 'hybrid',
            conversationUsage: 0,
            buttonUsage: 0,
            skillLevel: 'beginner',
            theme: 'light'
        };
        let chatHistory = [];

        // DOM Elements
        const chatInput = document.getElementById('chatInput');
        const sendBtn = document.getElementById('sendBtn');
        const chatContainer = document.getElementById('chatContainer');
        const orderItems = document.getElementById('orderItems');
        const subtotalEl = document.getElementById('subtotal');
        const discountLine = document.getElementById('discountLine');
        const discountLabel = document.getElementById('discountLabel');
        const discountAmount = document.getElementById('discountAmount');
        const customerInfo = document.getElementById('customerInfo');
        const customerName = document.getElementById('customerName');
        const taxEl = document.getElementById('tax');
        const totalEl = document.getElementById('total');
        const checkoutBtn = document.getElementById('checkoutBtn');
        const modeToggle = document.getElementById('modeToggle');
        // User level indicator removed from header
        const productGrid = document.getElementById('productGrid');
        const conversationArea = document.getElementById('conversationArea');
        const themeToggle = document.getElementById('themeToggle');
        const themeDropdown = document.getElementById('themeDropdown');
        const currentThemeSpan = document.getElementById('currentTheme');
        const toggleGrid = document.getElementById('toggleGrid');
        const gridToggleText = document.getElementById('gridToggleText');
        // User preference element removed with Smart Insights section
        const voiceBtn = document.getElementById('voiceBtn');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
            loadTheme();
            updateUserInterface();
        });

        function initializeApp() {
            // Load user preferences from localStorage
            const savedPrefs = localStorage.getItem('simplePosPrefs');
            if (savedPrefs) {
                userPreferences = { ...userPreferences, ...JSON.parse(savedPrefs) };
            }

            // Set initial focus
            chatInput.focus();

            // Update UI based on preferences
            updateUserLevel();
            
            // Initially align Current Order with Quick Select Products
            alignOrderWithProducts();

            // Handle window resize to maintain proper layout
            let resizeTimeout;
            let previousWidth = window.innerWidth;

            window.addEventListener('resize', function() {
                // Clear previous timeout
                clearTimeout(resizeTimeout);

                // Debounce the resize handling
                resizeTimeout = setTimeout(() => {
                    const currentWidth = window.innerWidth;
                    const rightColumn = document.getElementById('orderSummaryColumn');

                    // Check if we crossed the desktop breakpoint (1024px)
                    const wasDesktop = previousWidth >= 1024;
                    const isDesktop = currentWidth >= 1024;

                    if (rightColumn) {
                        if (!isDesktop) {
                            // On mobile/tablet, always reset margin
                            rightColumn.style.marginTop = '';
                        } else if (wasDesktop !== isDesktop) {
                            // Only realign when crossing breakpoints, not on every resize
                            rightColumn.style.marginTop = '';
                            setTimeout(() => {
                                alignOrderWithProducts();
                            }, 50);
                        }
                        // If staying on desktop, don't change the alignment
                    }

                    previousWidth = currentWidth;
                }, 250);
            });
        }

        function setupEventListeners() {
            // Chat input
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            sendBtn.addEventListener('click', sendMessage);
            
            // Product cards
            document.querySelectorAll('.product-card').forEach(card => {
                card.addEventListener('click', function() {
                    const product = this.dataset.product;
                    const price = parseFloat(this.dataset.price);
                    addToOrder(product, price);
                    userPreferences.buttonUsage++;
                    updateUserLevel();
                    
                    // Auto-generate conversation in hybrid mode
                    const currentMode = modeToggle.getAttribute('data-current-mode') || 'Smart Hybrid';
                    if (currentMode === 'Smart Hybrid') {
                        addAIMessage(`Added ${product} to your order! Anything else?`);
                    }
                    
                    // Visual feedback
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
            
            // Mode toggle
            modeToggle.addEventListener('click', toggleMode);
            
            // Theme toggle
            themeToggle.addEventListener('click', function() {
                themeDropdown.classList.toggle('hidden');
            });
            
            // Theme options
            document.querySelectorAll('.theme-option').forEach(option => {
                option.addEventListener('click', function() {
                    const theme = this.dataset.theme;
                    changeTheme(theme);
                    themeDropdown.classList.add('hidden');
                });
            });
            
            // Close theme dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!themeToggle.contains(e.target) && !themeDropdown.contains(e.target)) {
                    themeDropdown.classList.add('hidden');
                }
            });
            
            // Grid toggle
            toggleGrid.addEventListener('click', toggleProductGrid);
            
            // Checkout button
            checkoutBtn.addEventListener('click', processCheckout);
            
            // Suggestions
            document.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', function() {
                    chatInput.value = this.textContent.replace(/"/g, '');
                    sendMessage();
                });
            });
            
            // Discount dropdown functionality
            const discountBtn = document.getElementById('discountBtn');
            const discountDropdown = document.getElementById('discountDropdown');
            
            discountBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                discountDropdown.classList.toggle('hidden');
            });

            // Prevent discount dropdown from closing when clicking inside it
            discountDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
            
            // Customer dropdown functionality
            const customerBtn = document.getElementById('customerBtn');
            const customerDropdown = document.getElementById('customerDropdown');
            const customerNameInput = document.getElementById('customerNameInput');
            const addCustomerBtn = document.getElementById('addCustomerBtn');
            const cancelCustomerBtn = document.getElementById('cancelCustomerBtn');
            const removeCustomer = document.getElementById('removeCustomer');
            
            customerBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                customerDropdown.classList.toggle('hidden');
                if (!customerDropdown.classList.contains('hidden')) {
                    setTimeout(() => customerNameInput.focus(), 100);
                }
            });

            // Prevent customer dropdown from closing when clicking inside it
            customerDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
            
            addCustomerBtn.addEventListener('click', function() {
                const name = customerNameInput.value.trim();
                if (name) {
                    currentCustomer = name;
                    updateCustomerDisplay();
                    customerNameInput.value = '';
                    customerDropdown.classList.add('hidden');
                    addAIMessage(`Added customer: ${name}`);
                }
            });
            
            cancelCustomerBtn.addEventListener('click', function() {
                customerNameInput.value = '';
                customerDropdown.classList.add('hidden');
            });
            
            // Quick customer options
            document.querySelectorAll('.customer-quick').forEach(option => {
                option.addEventListener('click', function() {
                    const name = this.dataset.name;
                    currentCustomer = name;
                    updateCustomerDisplay();
                    customerDropdown.classList.add('hidden');
                    addAIMessage(`Added customer: ${name}`);
                });
            });
            
            // Remove customer
            removeCustomer.addEventListener('click', function() {
                currentCustomer = null;
                updateCustomerDisplay();
                addAIMessage("Customer removed from order.");
            });
            
            // Handle Enter key in customer input
            customerNameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    addCustomerBtn.click();
                }
            });
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                // Close discount dropdown if clicking outside
                if (!discountBtn.contains(e.target) && !discountDropdown.contains(e.target)) {
                    discountDropdown.classList.add('hidden');
                }
                // Close customer dropdown if clicking outside
                if (!customerBtn.contains(e.target) && !customerDropdown.contains(e.target)) {
                    customerDropdown.classList.add('hidden');
                }
            });
            
            // Handle discount option selection
            document.querySelectorAll('.discount-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const discount = this.dataset.discount;
                    chatInput.value = `apply ${discount}% discount`;
                    sendMessage();
                    discountDropdown.classList.add('hidden');
                });
            });
            
            // Quick actions (excluding discount and customer which are now handled above)
            document.querySelectorAll('.quick-action').forEach(action => {
                action.addEventListener('click', function() {
                    const text = this.textContent.trim();
                    if (text.includes('Receipt')) {
                        chatInput.value = 'print receipt';
                        sendMessage();
                    } else if (text.includes('Void')) {
                        chatInput.value = 'remove last item';
                        sendMessage();
                    }
                });
            });
            



            // Voice button (mock)
            voiceBtn.addEventListener('click', function() {
                this.classList.toggle('text-red-600');
                if (this.classList.contains('text-red-600')) {
                    addAIMessage("🎤 Voice listening active. Speak your order now...");
                    setTimeout(() => {
                        this.classList.remove('text-red-600');
                        addAIMessage("Voice input: '2 lattes and a croissant'");
                        processNaturalLanguage('2 lattes and a croissant');
                    }, 3000);
                }
            });
            
            // Clear chat
            document.getElementById('clearChat').addEventListener('click', function() {
                chatHistory = [];
                chatContainer.innerHTML = `
                    <div class="message-bubble mb-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: var(--accent-primary);">
                                <i class="fas fa-robot text-sm" style="color: var(--bg-primary);"></i>
                            </div>
                            <div class="rounded-2xl rounded-tl-md px-4 py-2 max-w-xs" style="background-color: var(--bg-accent);">
                                <p class="text-sm" style="color: var(--text-primary);">Chat cleared! How can I help you with your next order?</p>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Setup new UI elements
            setupCompactQuickActions();
            setupFloatingActionButton();
            setupHorizontalActionsBar();
        }

        // Setup functions for new UI elements
        function setupCompactQuickActions() {
            // Essential quick action buttons only
            const printReceiptBtn = document.getElementById('printReceiptBtn');
            const voidItemBtn = document.getElementById('voidItemBtn');

            if (printReceiptBtn) {
                printReceiptBtn.addEventListener('click', function() {
                    chatInput.value = 'print receipt';
                    sendMessage();
                });
            }

            if (voidItemBtn) {
                voidItemBtn.addEventListener('click', function() {
                    chatInput.value = 'remove last item';
                    sendMessage();
                });
            }
        }

        function setupFloatingActionButton() {
            const fabMain = document.getElementById('fabMain');
            const fabMenu = document.getElementById('fabMenu');
            const fabDiscount = document.getElementById('fabDiscount');
            const fabCustomer = document.getElementById('fabCustomer');
            const fabReceipt = document.getElementById('fabReceipt');
            const fabVoid = document.getElementById('fabVoid');

            if (fabMain && fabMenu) {
                fabMain.addEventListener('click', function() {
                    fabMenu.classList.toggle('active');
                    const icon = fabMain.querySelector('i');
                    if (fabMenu.classList.contains('active')) {
                        icon.className = 'fas fa-times';
                    } else {
                        icon.className = 'fas fa-plus';
                    }
                });

                // FAB action handlers
                if (fabDiscount) {
                    fabDiscount.addEventListener('click', function() {
                        document.getElementById('discountBtn').click();
                        fabMenu.classList.remove('active');
                        fabMain.querySelector('i').className = 'fas fa-plus';
                    });
                }

                if (fabCustomer) {
                    fabCustomer.addEventListener('click', function() {
                        document.getElementById('customerBtn').click();
                        fabMenu.classList.remove('active');
                        fabMain.querySelector('i').className = 'fas fa-plus';
                    });
                }

                if (fabReceipt) {
                    fabReceipt.addEventListener('click', function() {
                        chatInput.value = 'print receipt';
                        sendMessage();
                        fabMenu.classList.remove('active');
                        fabMain.querySelector('i').className = 'fas fa-plus';
                    });
                }

                if (fabVoid) {
                    fabVoid.addEventListener('click', function() {
                        chatInput.value = 'remove last item';
                        sendMessage();
                        fabMenu.classList.remove('active');
                        fabMain.querySelector('i').className = 'fas fa-plus';
                    });
                }

                // Close FAB menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!fabMain.contains(e.target) && !fabMenu.contains(e.target)) {
                        fabMenu.classList.remove('active');
                        fabMain.querySelector('i').className = 'fas fa-plus';
                    }
                });
            }
        }

        function setupHorizontalActionsBar() {
            const horizontalActionsBar = document.getElementById('horizontalActionsBar');
            const hDiscountBtn = document.getElementById('hDiscountBtn');
            const hCustomerBtn = document.getElementById('hCustomerBtn');
            const hReceiptBtn = document.getElementById('hReceiptBtn');
            const hVoidBtn = document.getElementById('hVoidBtn');
            const hCheckoutBtn = document.getElementById('hCheckoutBtn');
            const hClearBtn = document.getElementById('hClearBtn');

            // Horizontal action handlers
            if (hDiscountBtn) {
                hDiscountBtn.addEventListener('click', function() {
                    document.getElementById('discountBtn').click();
                });
            }

            if (hCustomerBtn) {
                hCustomerBtn.addEventListener('click', function() {
                    document.getElementById('customerBtn').click();
                });
            }

            if (hReceiptBtn) {
                hReceiptBtn.addEventListener('click', function() {
                    chatInput.value = 'print receipt';
                    sendMessage();
                });
            }

            if (hVoidBtn) {
                hVoidBtn.addEventListener('click', function() {
                    chatInput.value = 'remove last item';
                    sendMessage();
                });
            }

            if (hCheckoutBtn) {
                hCheckoutBtn.addEventListener('click', function() {
                    document.getElementById('checkoutBtn').click();
                });
            }

            if (hClearBtn) {
                hClearBtn.addEventListener('click', function() {
                    currentOrder = [];
                    updateOrderDisplay();
                    addAIMessage("Order cleared! Ready for a new order.");
                });
            }

            // Optional: Toggle horizontal bar visibility (can be controlled by user preference)
            // You can add a toggle button in the header if needed
        }

        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;
            
            // Add user message
            addUserMessage(message);
            chatInput.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            // Process message
            setTimeout(() => {
                processNaturalLanguage(message);
                hideTypingIndicator();
                userPreferences.conversationUsage++;
                updateUserLevel();
                savePreferences();
            }, 1000);
        }

        function addUserMessage(message) {
            const messageEl = document.createElement('div');
            messageEl.className = 'message-bubble mb-4';
            messageEl.innerHTML = `
                <div class="flex items-start space-x-2 sm:space-x-3 justify-end">
                    <div class="rounded-2xl rounded-tr-md px-3 sm:px-4 py-2 sm:py-3 max-w-xs sm:max-w-sm" style="background-color: var(--accent-primary);">
                        <p class="text-sm sm:text-base" style="color: var(--bg-primary);">${message}</p>
                    </div>
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: var(--accent-primary);">
                        <i class="fas fa-user text-sm" style="color: var(--bg-primary);"></i>
                    </div>
                </div>
            `;
            chatContainer.appendChild(messageEl);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            chatHistory.push({ type: 'user', message });
        }

        function addAIMessage(message) {
            const messageEl = document.createElement('div');
            messageEl.className = 'message-bubble mb-4';
            messageEl.innerHTML = `
                <div class="flex items-start space-x-2 sm:space-x-3">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0" style="background-color: var(--accent-primary);">
                        <i class="fas fa-robot text-sm" style="color: var(--bg-primary);"></i>
                    </div>
                    <div class="rounded-2xl rounded-tl-md px-3 sm:px-4 py-2 sm:py-3 max-w-xs sm:max-w-sm" style="background-color: var(--bg-accent);">
                        <p class="text-sm sm:text-base" style="color: var(--text-primary);">${message}</p>
                    </div>
                </div>
            `;
            chatContainer.appendChild(messageEl);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            chatHistory.push({ type: 'ai', message });
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').classList.remove('hidden');
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').classList.add('hidden');
        }

        function processNaturalLanguage(input) {
            const lowerInput = input.toLowerCase();
            
            // Extract numbers and products
            const products = [
                { name: 'coffee', price: 4.50, variations: ['coffee', 'americano', 'black coffee', 'decaf', 'decaf coffee'] },
                { name: 'latte', price: 5.25, variations: ['latte', 'cafe latte', 'medium latte', 'large latte', 'small latte', 'lattes'] },
                { name: 'croissant', price: 3.75, variations: ['croissant', 'croissants'] },
                { name: 'muffin', price: 4.00, variations: ['muffin', 'muffins'] },
                { name: 'sandwich', price: 7.50, variations: ['sandwich', 'sandwiches'] },
                { name: 'juice', price: 5.00, variations: ['juice', 'fresh juice', 'orange juice'] }
            ];
            
            // Handle checkout
            if (lowerInput.includes('checkout') || lowerInput.includes('pay') || lowerInput.includes('complete')) {
                if (isProcessingCheckout) {
                    addAIMessage("Checkout is already in progress. Please wait...");
                    return;
                }
                if (currentOrder.length > 0) {
                    addAIMessage(`Processing checkout for ${currentOrder.length} items totaling ${totalEl.textContent}. Payment method?`);
                    setTimeout(() => {
                        processCheckout();
                    }, 2000);
                } else {
                    addAIMessage("Your cart is empty. Add some items first!");
                }
                return;
            }
            
            // Handle discount - but don't return, continue processing products too
            let discountProcessed = false;
            if (lowerInput.includes('discount')) {
                const discountMatch = lowerInput.match(/(\d+)%/);
                if (discountMatch) {
                    const discount = parseInt(discountMatch[1]);
                    currentDiscount.percentage = discount;
                    addAIMessage(`Applied ${discount}% discount to your order!`);
                } else {
                    currentDiscount.percentage = 10;
                    addAIMessage("Applied 10% discount to your order!");
                }
                updateOrderDisplay();
                return; // Exit early to prevent duplicate message
                discountProcessed = true;
            }
            
            // Handle print receipt - but don't return, continue processing products too
            let printProcessed = false;
            if (lowerInput.includes('print') && lowerInput.includes('receipt')) {
                if (currentOrder.length > 0) {
                    generateReceipt();
                    printProcessed = true;
                } else {
                    addAIMessage("No items to print. Add some items to your order first!");
                    return; // Only return if there's nothing to print and no other commands
                }
            }
            
            // Handle item removal - process specific item removal
            let itemRemoved = false;
            if (lowerInput.includes('remove') || lowerInput.includes('delete')) {
                // Handle clear all commands - only if it's truly clearing everything
                if ((lowerInput.includes('all') && (lowerInput.includes('item') || lowerInput.includes('order'))) || 
                    lowerInput.includes('everything') || 
                    (lowerInput.includes('clear') && !products.some(p => p.variations.some(v => lowerInput.includes(v))))) {
                    currentOrder = [];
                    currentDiscount = { percentage: 0, amount: 0 };
                    currentCustomer = null;
                    addAIMessage("Cleared your entire order.");
                    updateOrderDisplay();
                    updateCustomerDisplay();
                    return;
                }
                
                // Handle remove last/recent
                if (lowerInput.includes('last') || lowerInput.includes('recent')) {
                    if (currentOrder.length > 0) {
                        const removed = currentOrder.pop();
                        addAIMessage(`Removed ${removed.name} from your order.`);
                        updateOrderDisplay();
                    } else {
                        addAIMessage("No items to remove.");
                    }
                    return;
                }
                
                // Handle remove specific items
                let removedItems = [];
                let removalQuantity = 1;
                let removeAll = false;
                
                // Check for "all [item]" pattern
                if (lowerInput.includes('all ') && products.some(p => p.variations.some(v => lowerInput.includes(v)))) {
                    removeAll = true;
                }
                
                // Check for quantity in removal command (only if not "all")
                if (!removeAll) {
                    const removalQuantityMatch = lowerInput.match(/remove\s+(\d+)\s+|delete\s+(\d+)\s+/);
                    if (removalQuantityMatch) {
                        removalQuantity = parseInt(removalQuantityMatch[1] || removalQuantityMatch[2]);
                    }
                }
                
                // Find items to remove by matching product variations
                products.forEach(product => {
                    const hasMatch = product.variations.some(variation => lowerInput.includes(variation));
                    if (hasMatch) {
                        // Find items with this product name in the order
                        let removed = 0;
                        const targetQuantity = removeAll ? Number.MAX_SAFE_INTEGER : removalQuantity;
                        
                        for (let i = currentOrder.length - 1; i >= 0 && removed < targetQuantity; i--) {
                            if (currentOrder[i].name.toLowerCase() === product.name.toLowerCase()) {
                                const removedItem = currentOrder.splice(i, 1)[0];
                                removedItems.push(removedItem.name);
                                removed++;
                            }
                        }
                    }
                });
                
                if (removedItems.length > 0) {
                    const itemsText = removedItems.length === 1 ? removedItems[0] : 
                        `${removedItems.length} items (${removedItems.join(', ')})`;
                    addAIMessage(`Removed ${itemsText} from your order.`);
                    updateOrderDisplay();
                    itemRemoved = true;
                    
                    // If this was purely a removal command, don't continue to product parsing
                    // Check if the input is primarily about removal (no add/want/get verbs)
                    const addWords = ['add', 'want', 'get', 'give', 'order', 'buy', 'purchase', 'take'];
                    const hasAddWords = addWords.some(word => lowerInput.includes(word));
                    const startsWithRemove = lowerInput.trim().startsWith('remove') || lowerInput.trim().startsWith('delete');
                    
                    if (startsWithRemove && !hasAddWords) {
                        return; // Pure removal command - don't continue to product parsing
                    }
                } else {
                    // No matching items found to remove
                    addAIMessage("I couldn't find those items in your order to remove.");
                    return;
                }
            }
            
            // Extract quantities and products using improved parsing
            let foundItems = [];
            let totalAdded = 0;
            
            // First, handle discount commands separately
            let processedInput = lowerInput;
            
            // Split input into segments by common delimiters - improved regex
            const segments = processedInput.split(/\s*(?:,\s*|\s+and\s+|&\s*)/);
            
            segments.forEach(segment => {
                segment = segment.trim();
                if (!segment || segment.includes('discount')) {
                    return; // Skip empty or discount segments
                }
                
                // Look for quantity patterns in each segment
                let quantity = 1;
                let productText = segment;
                
                // Check for number + product patterns
                const numberMatch = segment.match(/^(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+)/);
                if (numberMatch) {
                    const quantityStr = numberMatch[1];
                    productText = numberMatch[2].trim();
                    
                    // Convert word numbers to digits
                    const wordNumbers = {
                        'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
                        'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
                    };
                    
                    quantity = wordNumbers[quantityStr] || parseInt(quantityStr) || 1;
                } else {
                }
                
                // Find matching product for this segment
                let matchedProduct = null;
                let bestMatchLength = 0;
                
                products.forEach(product => {
                    product.variations.forEach(variation => {
                        // Check if the variation matches and is a good match
                        if (productText.includes(variation) && variation.length > bestMatchLength) {
                            matchedProduct = product;
                            bestMatchLength = variation.length;
                        }
                    });
                });
                
                if (matchedProduct) {
                    // Check if we already have this product in foundItems
                    const existingItem = foundItems.find(item => item.product.name === matchedProduct.name);
                    if (existingItem) {
                        existingItem.quantity += quantity;
                    } else {
                        foundItems.push({ product: matchedProduct, quantity });
                    }
                    totalAdded += quantity;
                } else {
                }
            });
            
            // Fallback: if no segments were parsed, try the old method for simple inputs
            if (foundItems.length === 0) {
                const matchedProducts = new Set();
                
                products.forEach(product => {
                    const hasMatch = product.variations.some(variation => lowerInput.includes(variation));
                    
                    if (hasMatch && !matchedProducts.has(product.name)) {
                        foundItems.push({ product, quantity: 1 });
                        matchedProducts.add(product.name);
                        totalAdded += 1;
                    }
                });
            }
            
            
            // Add items to order
            if (foundItems.length > 0) {
                foundItems.forEach(item => {
                    for (let i = 0; i < item.quantity; i++) {
                        addToOrder(item.product.name, item.product.price);
                    }
                });
                
                const itemsText = foundItems.map(item => 
                    `${item.quantity} ${item.product.name}${item.quantity > 1 ? 's' : ''}`
                ).join(', ');
                
                let responseMessage = `Added ${itemsText} to your order!`;
                if (discountProcessed) {
                    responseMessage += ` Discount applied.`;
                }
                if (printProcessed) {
                    responseMessage += ` Receipt printed.`;
                }
                responseMessage += ` Total: ${totalEl.textContent}. Anything else?`;
                addAIMessage(responseMessage);
                updateOrderDisplay();
            } else if (discountProcessed || printProcessed || itemRemoved) {
                // Only discount/print/removal was processed, no items added
                let message = "";
                if (discountProcessed) message += "Discount applied! ";
                if (printProcessed) message += "Receipt printed! ";
                if (itemRemoved && !discountProcessed && !printProcessed) message += "Items removed from order. ";
                if (discountProcessed && !printProcessed && !itemRemoved) message += "Add some items to see the discounted total.";
                addAIMessage(message.trim() || "Action completed!");
                updateOrderDisplay();
            } else {
                // Provide helpful response
                const suggestions = [
                    "I didn't catch that. Try saying something like '2 coffees and a muffin'",
                    "You can say things like 'add a latte' or 'I want 3 croissants'",
                    "Not sure what you meant. Try 'checkout' to complete your order, or add items like 'sandwich and juice'"
                ];
                addAIMessage(suggestions[Math.floor(Math.random() * suggestions.length)]);
            }
        }

        function addToOrder(name, price) {
            currentOrder.push({ name, price, id: Date.now() + Math.random() });
            updateOrderDisplay();
        }

        function updateOrderDisplay() {
            if (currentOrder.length === 0) {
                orderItems.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-shopping-bag text-4xl text-gray-300 mb-2"></i>
                        <p>No items yet</p>
                        <p class="text-sm">Start by typing or clicking products</p>
                    </div>
                `;
                checkoutBtn.disabled = true;
                checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium cursor-not-allowed transition-all duration-200";
                checkoutBtn.style.backgroundColor = 'var(--border-color)';
                checkoutBtn.style.color = 'var(--text-muted)';
                checkoutBtn.style.opacity = '1';
                checkoutBtn.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Complete Order';
            } else {
                // Group identical items by name and price, maintaining order
                const groupedItems = {};
                const orderTracker = []; // Track the order items first appear
                
                currentOrder.forEach((item, index) => {
                    const key = `${item.name}-${item.price}`;
                    if (groupedItems[key]) {
                        groupedItems[key].quantity += 1;
                        groupedItems[key].ids.push(item.id);
                    } else {
                        groupedItems[key] = {
                            name: item.name,
                            price: item.price,
                            quantity: 1,
                            ids: [item.id],
                            firstIndex: index // Track when this item type first appeared
                        };
                        orderTracker.push(key);
                    }
                });

                // Display grouped items in the order they first appeared
                orderItems.innerHTML = orderTracker.map(key => {
                    const group = groupedItems[key];
                    const totalPrice = group.price * group.quantity;
                    
                    return `
                        <div class="flex items-center py-2 border-b last:border-b-0" style="border-color: var(--border-color);">
                            <div class="flex-1 pr-4">
                                <span class="font-medium" style="color: var(--text-primary);">${group.name}</span>
                                <div class="text-xs" style="color: var(--text-muted);">$${group.price.toFixed(2)} each</div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="adjustQuantity('${group.name}', ${group.price}, -1)" class="w-6 h-6 rounded-full flex items-center justify-center text-sm flex-shrink-0 transition-colors" style="background-color: var(--bg-accent); color: var(--text-secondary);" onmouseover="this.style.backgroundColor='var(--bg-primary)'" onmouseout="this.style.backgroundColor='var(--bg-accent)'">
                                    <i class="fas fa-minus text-xs"></i>
                                </button>
                                <span class="text-sm font-medium w-6 text-center flex-shrink-0" style="color: var(--text-primary);">${group.quantity}</span>
                                <button onclick="adjustQuantity('${group.name}', ${group.price}, 1)" class="w-6 h-6 rounded-full flex items-center justify-center text-sm flex-shrink-0 transition-colors" style="background-color: var(--bg-accent); color: var(--text-secondary);" onmouseover="this.style.backgroundColor='var(--bg-primary)'" onmouseout="this.style.backgroundColor='var(--bg-accent)'">
                                    <i class="fas fa-plus text-xs"></i>
                                </button>
                                <span class="w-16 text-right flex-shrink-0 font-medium" style="color: var(--text-primary);">$${totalPrice.toFixed(2)}</span>
                                <button onclick="removeItemGroup('${group.name}', ${group.price})" class="text-sm w-6 flex-shrink-0 flex justify-center transition-colors" style="color: var(--error-color);" onmouseover="this.style.opacity='0.7'" onmouseout="this.style.opacity='1'">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
                
                // Only enable checkout if not currently processing
                if (!isProcessingCheckout) {
                    checkoutBtn.disabled = false;
                    checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium transition-all duration-200";
                    checkoutBtn.style.backgroundColor = 'var(--success-color)';
                    checkoutBtn.style.color = 'white';
                    checkoutBtn.style.opacity = '1';
                    checkoutBtn.style.boxShadow = ''; // Clear any processing shadow
                    checkoutBtn.style.cursor = 'pointer';
                    checkoutBtn.onmouseover = function() {
                        if (!isProcessingCheckout) {
                            this.style.backgroundColor = '#059669'; // Darker green on hover
                            this.style.transform = 'translateY(-1px)';
                            this.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.3)';
                        }
                    };
                    checkoutBtn.onmouseout = function() {
                        if (!isProcessingCheckout) {
                            this.style.backgroundColor = 'var(--success-color)';
                            this.style.transform = 'translateY(0)';
                            this.style.boxShadow = '';
                        }
                    };
                    checkoutBtn.innerHTML = '<i class="fas fa-credit-card mr-2"></i>Complete Order';
                }
            }
            
            // Update totals
            const subtotal = currentOrder.reduce((sum, item) => sum + item.price, 0);
            
            // Calculate discount
            let discountedSubtotal = subtotal;
            if (currentDiscount.percentage > 0) {
                currentDiscount.amount = subtotal * (currentDiscount.percentage / 100);
                discountedSubtotal = subtotal - currentDiscount.amount;
                
                // Show discount line
                discountLine.classList.remove('hidden');
                discountLabel.textContent = `Discount (${currentDiscount.percentage}%):`;
                discountAmount.textContent = `-$${currentDiscount.amount.toFixed(2)}`;
            } else {
                // Hide discount line
                discountLine.classList.add('hidden');
                currentDiscount.amount = 0;
            }
            
            const tax = discountedSubtotal * 0.085;
            const total = discountedSubtotal + tax;
            
            subtotalEl.textContent = `$${subtotal.toFixed(2)}`;
            taxEl.textContent = `$${tax.toFixed(2)}`;
            totalEl.textContent = `$${total.toFixed(2)}`;

            // Update compact summary
            updateCompactSummary(subtotal, tax, total);
        }

        function updateCompactSummary(subtotal, tax, total) {
            const subtotalCompact = document.getElementById('subtotalCompact');
            const taxCompact = document.getElementById('taxCompact');
            const totalCompact = document.getElementById('totalCompact');
            const discountLineCompact = document.getElementById('discountLineCompact');
            const discountLabelCompact = document.getElementById('discountLabelCompact');
            const discountAmountCompact = document.getElementById('discountAmountCompact');

            if (subtotalCompact) subtotalCompact.textContent = `$${subtotal.toFixed(2)}`;
            if (taxCompact) taxCompact.textContent = `$${tax.toFixed(2)}`;
            if (totalCompact) totalCompact.textContent = `$${total.toFixed(2)}`;

            // Handle discount display in compact summary
            if (currentDiscount.percentage > 0 && discountLineCompact) {
                discountLineCompact.classList.remove('hidden');
                if (discountLabelCompact) discountLabelCompact.textContent = `Discount (${currentDiscount.percentage}%):`;
                if (discountAmountCompact) discountAmountCompact.textContent = `-$${currentDiscount.amount.toFixed(2)}`;
            } else if (discountLineCompact) {
                discountLineCompact.classList.add('hidden');
            }
        }

        function updateCustomerDisplay() {
            if (currentCustomer) {
                customerInfo.classList.remove('hidden');
                customerName.textContent = currentCustomer;
            } else {
                customerInfo.classList.add('hidden');
            }
        }

        function generateReceipt() {
            // Calculate totals
            const subtotal = currentOrder.reduce((sum, item) => sum + item.price, 0);
            let discountedSubtotal = subtotal;
            
            if (currentDiscount.percentage > 0) {
                discountedSubtotal = subtotal - currentDiscount.amount;
            }
            
            const tax = discountedSubtotal * 0.085;
            const total = discountedSubtotal + tax;
            
            // Generate receipt content
            const now = new Date();
            const dateStr = now.toLocaleDateString();
            const timeStr = now.toLocaleTimeString();
            
            let receiptContent = `
================================
       SIMPLEPOS RECEIPT
================================
Date: ${dateStr}
Time: ${timeStr}
${currentCustomer ? `Customer: ${currentCustomer}` : ''}

--------------------------------
ITEMS:
`;

            currentOrder.forEach(item => {
                receiptContent += `${item.name.padEnd(20)} $${item.price.toFixed(2)}\n`;
            });

            receiptContent += `
--------------------------------
Subtotal:           $${subtotal.toFixed(2)}`;

            if (currentDiscount.percentage > 0) {
                receiptContent += `
Discount (${currentDiscount.percentage}%):      -$${currentDiscount.amount.toFixed(2)}`;
            }

            receiptContent += `
Tax (8.5%):         $${tax.toFixed(2)}
TOTAL:              $${total.toFixed(2)}

================================
     Thank you for your visit!
================================`;

            // Create and show receipt modal/popup
            showReceiptModal(receiptContent);
            
            addAIMessage("Receipt generated! 🧾 Check the receipt window or print dialog.");
        }

        function showReceiptModal(content) {
            // Create modal backdrop
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Receipt</h3>
                        <button id="closeReceipt" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <pre class="text-sm font-mono text-gray-800 whitespace-pre-wrap">${content}</pre>
                    </div>
                    <div class="flex space-x-3">
                        <button id="printReceipt" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium">
                            <i class="fas fa-print mr-2"></i>Print
                        </button>
                        <button id="closeReceiptBtn" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 font-medium">
                            Close
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Handle close buttons
            const closeButtons = modal.querySelectorAll('#closeReceipt, #closeReceiptBtn');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                });
            });
            
            // Handle print button
            const printBtn = modal.querySelector('#printReceipt');
            printBtn.addEventListener('click', () => {
                // Create a new window for printing
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>Receipt</title>
                            <style>
                                body { font-family: 'Courier New', monospace; margin: 20px; }
                                pre { white-space: pre-wrap; }
                            </style>
                        </head>
                        <body>
                            <pre>${content}</pre>
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.print();
                printWindow.close();
                
                // Close the modal
                document.body.removeChild(modal);
                addAIMessage("Receipt sent to printer! 🖨️");
            });
            
            // Close modal when clicking backdrop
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        function removeItem(id) {
            currentOrder = currentOrder.filter(item => item.id !== parseFloat(id));
            updateOrderDisplay();
            addAIMessage("Item removed from your order.");
        }

        function removeItemGroup(name, price) {
            const initialCount = currentOrder.length;
            currentOrder = currentOrder.filter(item => !(item.name === name && item.price === price));
            const removedCount = initialCount - currentOrder.length;
            
            updateOrderDisplay();
            addAIMessage(`Removed all ${name} items (${removedCount} items) from your order.`);
        }

        function adjustQuantity(name, price, change) {
            if (change > 0) {
                // Add one more item
                addToOrder(name, price);
                addAIMessage(`Added 1 more ${name} to your order.`);
            } else if (change < 0) {
                // Check how many of this item type we currently have
                const itemsOfType = currentOrder.filter(item => item.name === name && item.price === price);
                const currentQuantity = itemsOfType.length;
                
                // Remove one item of this type
                const itemIndex = currentOrder.findIndex(item => item.name === name && item.price === price);
                if (itemIndex !== -1) {
                    currentOrder.splice(itemIndex, 1);
                    updateOrderDisplay();
                    
                    if (currentQuantity === 1) {
                        addAIMessage(`Removed ${name} from your order.`);
                    } else {
                        addAIMessage(`Removed 1 ${name} from your order. ${currentQuantity - 1} remaining.`);
                    }
                }
            }
        }

        function processCheckout() {
            if (currentOrder.length === 0) {
                addAIMessage("Your cart is empty! Add some items first.");
                return;
            }

            if (isProcessingCheckout) {
                addAIMessage("Checkout is already in progress. Please wait...");
                return;
            }

            // Set checkout state and disable button
            isProcessingCheckout = true;
            checkoutBtn.disabled = true;
            checkoutBtn.className = "w-full mt-4 py-3 rounded-lg font-medium cursor-not-allowed transition-all duration-200";
            // Remove any existing hover handlers
            checkoutBtn.onmouseover = null;
            checkoutBtn.onmouseout = null;
            // Set modern processing colors - orange/amber for active processing
            checkoutBtn.style.backgroundColor = '#f59e0b'; // Modern amber/orange for processing
            checkoutBtn.style.color = 'white';
            checkoutBtn.style.opacity = '1';
            checkoutBtn.style.boxShadow = '0 4px 12px rgba(245, 158, 11, 0.3)';
            checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing Payment...';

            addAIMessage(`Processing payment of ${totalEl.textContent}... Payment approved! 🎉 Receipt sent. Thank you!`);

            // Reset order
            setTimeout(() => {
                currentOrder = [];
                currentDiscount = { percentage: 0, amount: 0 };
                currentCustomer = null;
                isProcessingCheckout = false; // Reset checkout state
                // Clear processing styles
                checkoutBtn.style.boxShadow = '';
                updateOrderDisplay();
                updateCustomerDisplay();
                addAIMessage("Ready for your next order! 😊");
            }, 3000);
        }

        function toggleMode() {
            const modes = ['Smart Hybrid', 'Conversation Only', 'Traditional Only'];
            const currentMode = modeToggle.getAttribute('data-current-mode') || 'Smart Hybrid';
            const currentIndex = modes.indexOf(currentMode);
            const nextIndex = (currentIndex + 1) % modes.length;
            const nextMode = modes[nextIndex];

            // Update the data attribute
            modeToggle.setAttribute('data-current-mode', nextMode);

            let icon = 'fas fa-brain';
            let shortName = 'Hybrid';
            if (nextMode === 'Conversation Only') {
                icon = 'fas fa-comments';
                shortName = 'Chat';
            } else if (nextMode === 'Traditional Only') {
                icon = 'fas fa-th-large';
                shortName = 'Buttons';
            }

            // Update button content with responsive text
            modeToggle.innerHTML = `<i class="${icon} mr-1 sm:mr-2"></i><span class="hidden sm:inline">${nextMode}</span><span class="sm:hidden">${shortName}</span>`;

            // Update mode indicator badge
            const modeIndicator = document.getElementById('modeIndicator');
            if (modeIndicator) {
                modeIndicator.textContent = `${shortName} Mode`;
            }

            // Get the parent container that has the space-y-6 class
            const leftColumn = productGrid.parentElement;

            // Adjust interface based on mode
            if (nextMode === 'Conversation Only') {
                productGrid.style.display = 'none';
                conversationArea.style.display = 'block';
                // Remove spacing when only conversation area is visible
                leftColumn.classList.remove('space-y-6');
                addAIMessage("Switched to conversation-only mode. Just type what you want!");
            } else if (nextMode === 'Traditional Only') {
                productGrid.style.display = 'block';
                conversationArea.style.display = 'none';
                // Remove spacing when only product grid is visible
                leftColumn.classList.remove('space-y-6');
                // Don't add AI message when conversation area is hidden
            } else {
                productGrid.style.display = 'block';
                conversationArea.style.display = 'block';
                // Restore spacing when both elements are visible
                leftColumn.classList.add('space-y-6');
                addAIMessage("Switched to hybrid mode. Use conversation or click products - whatever feels natural!");
            }
            
            userPreferences.preferredMode = nextMode.toLowerCase().replace(' ', '_');
            savePreferences();
        }

        function toggleProductGrid() {
            const container = document.getElementById('productContainer');
            if (container.style.display === 'none') {
                container.style.display = 'grid';
                gridToggleText.textContent = 'Hide Grid';
                toggleGrid.querySelector('i').className = 'fas fa-eye mr-1';
            } else {
                container.style.display = 'none';
                gridToggleText.textContent = 'Show Grid';
                toggleGrid.querySelector('i').className = 'fas fa-eye-slash mr-1';
            }
        }

        function updateUserLevel() {
            const total = userPreferences.conversationUsage + userPreferences.buttonUsage;
            const conversationRatio = total > 0 ? userPreferences.conversationUsage / total : 0;

            if (total < 5) {
                userPreferences.skillLevel = 'learning';
            } else if (conversationRatio > 0.7) {
                userPreferences.skillLevel = 'expert';
            } else if (conversationRatio > 0.3) {
                userPreferences.skillLevel = 'intermediate';
            } else {
                userPreferences.skillLevel = 'traditional';
            }

                    }


        function updateUserInterface() {
            // Adapt interface based on user skill level
            const skillLevel = userPreferences.skillLevel;
            
            if (skillLevel === 'expert') {
                // Hide product grid by default for experts
                document.getElementById('productContainer').style.display = 'none';
                gridToggleText.textContent = 'Show Grid';
                toggleGrid.querySelector('i').className = 'fas fa-eye-slash mr-1';
            }
        }

        function alignOrderWithProducts() {
            try {
                const rightColumn = document.getElementById('orderSummaryColumn');
                const productGrid = document.getElementById('productGrid');

                if (!rightColumn || !productGrid) {
                    return;
                }

                // Check if we're on desktop (lg breakpoint and above)
                if (window.innerWidth >= 1024) {
                    // Calculate alignment
                    // Wait for layout to stabilize before measuring
                    requestAnimationFrame(() => {
                        try {
                            const productGridRect = productGrid.getBoundingClientRect();
                            const rightColumnRect = rightColumn.getBoundingClientRect();

                            // Calculate the offset needed to align the tops
                            const offset = productGridRect.top - rightColumnRect.top;

                            // Apply the offset as margin-top, but only if it's positive and reasonable
                            // Also check that we're not already aligned (within 5px tolerance)
                            if (offset > 5 && offset < 200) {
                                rightColumn.style.marginTop = `${offset}px`;
                            } else if (Math.abs(offset) <= 5) {
                                // Already aligned, don't change
                                return;
                            } else {
                                rightColumn.style.marginTop = '';
                            }
                        } catch (innerError) {
                            console.error('Error in alignment calculation:', innerError);
                        }
                    });
                } else {
                    // On mobile/tablet, always reset to default layout
                    rightColumn.style.marginTop = '';
                }
            } catch (error) {
                console.error('Error in alignOrderWithProducts:', error);
                // Fallback: reset to default
                const rightColumn = document.getElementById('orderSummaryColumn');
                if (rightColumn) {
                    rightColumn.style.marginTop = '';
                }
            }
        }

        function savePreferences() {
            localStorage.setItem('simplePosPrefs', JSON.stringify(userPreferences));
        }

        // Theme Management Functions - Modern POS 2024
        function changeTheme(themeName) {
            const themes = {
                light: { 
                    name: 'Light Mode', 
                    icon: 'fas fa-sun',
                    class: '' 
                },
                dark: { 
                    name: 'Dark Mode', 
                    icon: 'fas fa-moon',
                    class: 'theme-dark' 
                },
                contrast: { 
                    name: 'High Contrast', 
                    icon: 'fas fa-eye',
                    class: 'theme-contrast' 
                },
                cafe: { 
                    name: 'Café', 
                    icon: 'fas fa-coffee',
                    class: 'theme-cafe' 
                },
                corporate: { 
                    name: 'Corporate', 
                    icon: 'fas fa-building',
                    class: 'theme-corporate' 
                },
                restaurant: { 
                    name: 'Restaurant', 
                    icon: 'fas fa-utensils',
                    class: 'theme-restaurant' 
                },
                health: { 
                    name: 'Health', 
                    icon: 'fas fa-leaf',
                    class: 'theme-health' 
                }
            };

            const theme = themes[themeName];
            if (!theme) return;

            // Remove all theme classes
            document.body.classList.remove('theme-dark', 'theme-contrast', 'theme-cafe', 'theme-corporate', 'theme-restaurant', 'theme-health');
            
            // Add new theme class (if not default light)
            if (theme.class) {
                document.body.classList.add(theme.class);
            }

            // Update theme state
            currentTheme = themeName;
            userPreferences.theme = themeName;

            // Update UI elements
            currentThemeSpan.textContent = theme.name;
            themeToggle.querySelector('i').className = `${theme.icon} mr-1 sm:mr-2`;

            // Save preference
            savePreferences();
        }

        function loadTheme() {
            const savedPrefs = localStorage.getItem('simplePosPrefs');
            if (savedPrefs) {
                const prefs = JSON.parse(savedPrefs);
                if (prefs.theme) {
                    changeTheme(prefs.theme);
                }
            }
        }

        // Make functions globally accessible
        window.removeItem = removeItem;
        window.removeItemGroup = removeItemGroup;
        window.adjustQuantity = adjustQuantity;
        
        // Auto-save preferences periodically
        setInterval(savePreferences, 30000);
    </script>
</body>
</html>
